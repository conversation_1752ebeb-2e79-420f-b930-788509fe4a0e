<div class="flex flex-col gap-6">
    <x-auth-header :title="__('Create an account')" :description="__('Enter your details below to create your account')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form wire:submit="register" class="flex flex-col gap-6">
        <!-- Name -->
        <flux:input
            wire:model="name"
            :label="__('Name')"
            type="text"
            required
            autofocus
            autocomplete="name"
            :placeholder="__('Full name')"
        />

        <!-- Email Address -->
        <flux:input
            wire:model="email"
            :label="__('Email address')"
            type="email"
            required
            autocomplete="email"
            placeholder="<EMAIL>"
        />

        <!-- Password -->
        <flux:input
            wire:model="password"
            :label="__('Password')"
            type="password"
            required
            autocomplete="new-password"
            :placeholder="__('Password')"
            viewable
        />

        <!-- Confirm Password -->
        <flux:input
            wire:model="password_confirmation"
            :label="__('Confirm password')"
            type="password"
            required
            autocomplete="new-password"
            :placeholder="__('Confirm password')"
            viewable
        />

        <!-- Role -->
        <flux:select wire:model="role" :label="__('Role')" required>
            @foreach(\App\Enums\UserRole::cases() as $role)
                <flux:option value="{{ $role->value }}">{{ $role->label() }}</flux:option>
            @endforeach
        </flux:select>

        <!-- Phone Number -->
        <flux:input
            wire:model="phone_number"
            :label="__('Phone Number')"
            type="tel"
            autocomplete="tel"
            :placeholder="__('Phone number')"
        />

        <!-- NID Number -->
        <flux:input
            wire:model="nid_number"
            :label="__('NID Number')"
            type="text"
            :placeholder="__('National ID number')"
        />

        <!-- NID Image -->
        <div>
            <flux:label>{{ __('NID Image') }}</flux:label>
            <flux:input
                wire:model="nid_image"
                type="file"
                accept="image/*"
                class="mt-1"
            />
            @if ($nid_image)
                <div class="mt-2">
                    <img src="{{ $nid_image->temporaryUrl() }}" class="h-20 w-auto rounded-lg object-cover">
                </div>
            @endif
        </div>

        <!-- Profile Image -->
        <div>
            <flux:label>{{ __('Profile Image') }}</flux:label>
            <flux:input
                wire:model="image"
                type="file"
                accept="image/*"
                class="mt-1"
            />
            @if ($image)
                <div class="mt-2">
                    <img src="{{ $image->temporaryUrl() }}" class="h-20 w-20 rounded-full object-cover">
                </div>
            @endif
        </div>

        <!-- Permanent Address -->
        <flux:textarea
            wire:model="permanent_address"
            :label="__('Permanent Address')"
            :placeholder="__('Enter your permanent address')"
            rows="3"
        />

        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" class="w-full">
                {{ __('Create account') }}
            </flux:button>
        </div>
    </form>

    <div class="space-x-1 rtl:space-x-reverse text-center text-sm text-zinc-600 dark:text-zinc-400">
        {{ __('Already have an account?') }}
        <flux:link :href="route('login')" wire:navigate>{{ __('Log in') }}</flux:link>
    </div>
</div>
