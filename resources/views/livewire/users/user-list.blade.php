<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Users</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage all users in the system</p>
        </div>
        <flux:button variant="primary" wire:click="$dispatch('open-user-modal')">
            Add User
        </flux:button>
    </div>

    <!-- Filters -->
    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div class="flex flex-col gap-4 sm:flex-row sm:items-center">
            <!-- Search -->
            <div class="w-full sm:w-64">
                <flux:input
                    wire:model.live.debounce.300ms="search"
                    placeholder="Search users..."
                    type="search"
                />
            </div>

            <!-- Role Filter -->
            <div class="w-full sm:w-48">
                <flux:select wire:model.live="roleFilter" placeholder="All Roles">
                    <flux:option value="">All Roles</flux:option>
                    @foreach($roles as $role)
                        <flux:option value="{{ $role->value }}">{{ $role->label() }}</flux:option>
                    @endforeach
                </flux:select>
            </div>

            <!-- Clear Filters -->
            @if($search || $roleFilter)
                <flux:button variant="ghost" wire:click="clearFilters">
                    Clear Filters
                </flux:button>
            @endif
        </div>

        <!-- Results Count -->
        <div class="text-sm text-gray-600 dark:text-gray-400">
            {{ $users->total() }} {{ Str::plural('user', $users->total()) }} found
        </div>
    </div>

    <!-- Users Table -->
    <div class="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <button wire:click="sortBy('name')" class="flex items-center space-x-1 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                                <span>Name</span>
                                @if($sortBy === 'name')
                                    <flux:icon.chevron-up class="h-4 w-4 {{ $sortDirection === 'desc' ? 'rotate-180' : '' }}" />
                                @endif
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left">
                            <button wire:click="sortBy('email')" class="flex items-center space-x-1 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                                <span>Email</span>
                                @if($sortBy === 'email')
                                    <flux:icon.chevron-up class="h-4 w-4 {{ $sortDirection === 'desc' ? 'rotate-180' : '' }}" />
                                @endif
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left">
                            <button wire:click="sortBy('role')" class="flex items-center space-x-1 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                                <span>Role</span>
                                @if($sortBy === 'role')
                                    <flux:icon.chevron-up class="h-4 w-4 {{ $sortDirection === 'desc' ? 'rotate-180' : '' }}" />
                                @endif
                            </button>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                            Phone
                        </th>
                        <th class="px-6 py-3 text-left">
                            <button wire:click="sortBy('created_at')" class="flex items-center space-x-1 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                                <span>Joined</span>
                                @if($sortBy === 'created_at')
                                    <flux:icon.chevron-up class="h-4 w-4 {{ $sortDirection === 'desc' ? 'rotate-180' : '' }}" />
                                @endif
                            </button>
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                    @forelse($users as $user)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 flex-shrink-0">
                                        @if($user->image)
                                            <img class="h-10 w-10 rounded-full object-cover" src="{{ $user->image_url }}" alt="{{ $user->name }}">
                                        @else
                                            <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ $user->initials() }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $user->name }}</div>
                                        @if($user->nid_number)
                                            <div class="text-sm text-gray-500 dark:text-gray-400">NID: {{ $user->nid_number }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white">{{ $user->email }}</div>
                                @if($user->email_verified_at)
                                    <div class="text-xs text-green-600 dark:text-green-400">Verified</div>
                                @else
                                    <div class="text-xs text-red-600 dark:text-red-400">Unverified</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <flux:badge color="{{ $user->role->color() }}" size="sm">
                                    {{ $user->role->label() }}
                                </flux:badge>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $user->phone_number ?: '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $user->created_at->format('M j, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <flux:button size="sm" variant="ghost" wire:click="$dispatch('view-user', { id: {{ $user->id }} })">
                                        View
                                    </flux:button>
                                    <flux:button size="sm" variant="ghost" wire:click="$dispatch('edit-user', { id: {{ $user->id }} })">
                                        Edit
                                    </flux:button>
                                    <flux:button size="sm" variant="danger" wire:click="$dispatch('delete-user', { id: {{ $user->id }} })">
                                        Delete
                                    </flux:button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500 dark:text-gray-400">
                                    <flux:icon.users class="mx-auto h-12 w-12 mb-4" />
                                    <h3 class="text-lg font-medium mb-2">No users found</h3>
                                    <p class="text-sm">{{ $search || $roleFilter ? 'Try adjusting your search or filters.' : 'Get started by adding your first user.' }}</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if($users->hasPages())
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700 dark:text-gray-300">
                Showing {{ $users->firstItem() }} to {{ $users->lastItem() }} of {{ $users->total() }} results
            </div>
            {{ $users->links() }}
        </div>
    @endif
</div>
