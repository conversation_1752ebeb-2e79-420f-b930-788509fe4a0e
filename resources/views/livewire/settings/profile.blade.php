<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Profile')" :subheading="__('Update your profile information')">
        <form wire:submit="updateProfileInformation" class="my-6 w-full space-y-6">
            <flux:input wire:model="name" :label="__('Name')" type="text" required autofocus autocomplete="name" />

            <div>
                <flux:input wire:model="email" :label="__('Email')" type="email" required autocomplete="email" />

                @if (auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail &&! auth()->user()->hasVerifiedEmail())
                    <div>
                        <flux:text class="mt-4">
                            {{ __('Your email address is unverified.') }}

                            <flux:link class="text-sm cursor-pointer" wire:click.prevent="resendVerificationNotification">
                                {{ __('Click here to re-send the verification email.') }}
                            </flux:link>
                        </flux:text>

                        @if (session('status') === 'verification-link-sent')
                            <flux:text class="mt-2 font-medium !dark:text-green-400 !text-green-600">
                                {{ __('A new verification link has been sent to your email address.') }}
                            </flux:text>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Role -->
            <flux:select wire:model="role" :label="__('Role')" required>
                @foreach(\App\Enums\UserRole::cases() as $roleOption)
                    <flux:option value="{{ $roleOption->value }}">{{ $roleOption->label() }}</flux:option>
                @endforeach
            </flux:select>

            <!-- Phone Number -->
            <flux:input
                wire:model="phone_number"
                :label="__('Phone Number')"
                type="tel"
                autocomplete="tel"
                :placeholder="__('Phone number')"
            />

            <!-- NID Number -->
            <flux:input
                wire:model="nid_number"
                :label="__('NID Number')"
                type="text"
                :placeholder="__('National ID number')"
            />

            <!-- NID Image -->
            <div>
                <flux:label>{{ __('NID Image') }}</flux:label>
                <flux:input
                    wire:model="nid_image"
                    type="file"
                    accept="image/*"
                    class="mt-1"
                />
                @if ($nid_image)
                    <div class="mt-2">
                        <img src="{{ $nid_image->temporaryUrl() }}" class="h-20 w-auto rounded-lg object-cover">
                    </div>
                @elseif ($current_nid_image)
                    <div class="mt-2">
                        <img src="{{ asset('storage/' . $current_nid_image) }}" class="h-20 w-auto rounded-lg object-cover">
                    </div>
                @endif
            </div>

            <!-- Profile Image -->
            <div>
                <flux:label>{{ __('Profile Image') }}</flux:label>
                <flux:input
                    wire:model="image"
                    type="file"
                    accept="image/*"
                    class="mt-1"
                />
                @if ($image)
                    <div class="mt-2">
                        <img src="{{ $image->temporaryUrl() }}" class="h-20 w-20 rounded-full object-cover">
                    </div>
                @elseif ($current_image)
                    <div class="mt-2">
                        <img src="{{ asset('storage/' . $current_image) }}" class="h-20 w-20 rounded-full object-cover">
                    </div>
                @endif
            </div>

            <!-- Permanent Address -->
            <flux:textarea
                wire:model="permanent_address"
                :label="__('Permanent Address')"
                :placeholder="__('Enter your permanent address')"
                rows="3"
            />

            <div class="flex items-center gap-4">
                <div class="flex items-center justify-end">
                    <flux:button variant="primary" type="submit" class="w-full">{{ __('Save') }}</flux:button>
                </div>

                <x-action-message class="me-3" on="profile-updated">
                    {{ __('Saved.') }}
                </x-action-message>
            </div>
        </form>

        <livewire:settings.delete-user-form />
    </x-settings.layout>
</section>
