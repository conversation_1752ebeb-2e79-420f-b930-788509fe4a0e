<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['owner', 'tenant', 'manager'])->default('tenant')->after('email');
            $table->string('phone_number')->nullable()->after('role');
            $table->string('nid_number')->nullable()->after('phone_number');
            $table->string('nid_image')->nullable()->after('nid_number');
            $table->string('image')->nullable()->after('nid_image');
            $table->text('permanent_address')->nullable()->after('image');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'role',
                'phone_number',
                'nid_number',
                'nid_image',
                'image',
                'permanent_address'
            ]);
        });
    }
};
