<?php

namespace App\Enums;

enum UserRole: string
{
    case OWNER = 'owner';
    case TENANT = 'tenant';
    case MANAGER = 'manager';

    /**
     * Get all role values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get role label for display
     */
    public function label(): string
    {
        return match($this) {
            self::OWNER => 'Owner',
            self::TENANT => 'Tenant',
            self::MANAGER => 'Manager',
        };
    }

    /**
     * Get role description
     */
    public function description(): string
    {
        return match($this) {
            self::OWNER => 'Property owner with full access to manage properties and tenants',
            self::TENANT => 'Tenant with access to their rental information and payments',
            self::MANAGER => 'Property manager with administrative access to manage properties',
        };
    }

    /**
     * Get role color for UI display
     */
    public function color(): string
    {
        return match($this) {
            self::OWNER => 'blue',
            self::TENANT => 'green',
            self::MANAGER => 'purple',
        };
    }

    /**
     * Check if role has administrative privileges
     */
    public function isAdmin(): bool
    {
        return in_array($this, [self::OWNER, self::MANAGER]);
    }

    /**
     * Check if role can manage users
     */
    public function canManageUsers(): bool
    {
        return $this === self::OWNER;
    }

    /**
     * Check if role can manage properties
     */
    public function canManageProperties(): bool
    {
        return in_array($this, [self::OWNER, self::MANAGER]);
    }
}
