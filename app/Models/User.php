<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserRole;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone_number',
        'nid_number',
        'nid_image',
        'image',
        'permanent_address',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'role' => UserRole::class,
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn ($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    /**
     * Get the user's profile image URL
     */
    public function getImageUrlAttribute(): ?string
    {
        if (!$this->image) {
            return null;
        }

        return asset('storage/' . $this->image);
    }

    /**
     * Get the user's NID image URL
     */
    public function getNidImageUrlAttribute(): ?string
    {
        if (!$this->nid_image) {
            return null;
        }

        return asset('storage/' . $this->nid_image);
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(UserRole $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }

    /**
     * Check if user is an owner
     */
    public function isOwner(): bool
    {
        return $this->hasRole(UserRole::OWNER);
    }

    /**
     * Check if user is a tenant
     */
    public function isTenant(): bool
    {
        return $this->hasRole(UserRole::TENANT);
    }

    /**
     * Check if user is a manager
     */
    public function isManager(): bool
    {
        return $this->hasRole(UserRole::MANAGER);
    }

    /**
     * Check if user has administrative privileges
     */
    public function isAdmin(): bool
    {
        return $this->role->isAdmin();
    }

    /**
     * Check if user can manage other users
     */
    public function canManageUsers(): bool
    {
        return $this->role->canManageUsers();
    }

    /**
     * Check if user can manage properties
     */
    public function canManageProperties(): bool
    {
        return $this->role->canManageProperties();
    }

    /**
     * Scope to filter users by role
     */
    public function scopeByRole($query, UserRole $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope to filter users by multiple roles
     */
    public function scopeByRoles($query, array $roles)
    {
        return $query->whereIn('role', $roles);
    }

    /**
     * Scope to get only admin users (owners and managers)
     */
    public function scopeAdmins($query)
    {
        return $query->whereIn('role', [UserRole::OWNER, UserRole::MANAGER]);
    }
}
