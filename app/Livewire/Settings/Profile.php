<?php

namespace App\Livewire\Settings;

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithFileUploads;

class Profile extends Component
{
    use WithFileUploads;

    public string $name = '';
    public string $email = '';
    public UserRole $role;
    public string $phone_number = '';
    public string $nid_number = '';
    public $nid_image;
    public $image;
    public string $permanent_address = '';
    public ?string $current_nid_image = null;
    public ?string $current_image = null;

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $user = Auth::user();
        $this->name = $user->name;
        $this->email = $user->email;
        $this->role = $user->role ?? UserRole::TENANT;
        $this->phone_number = $user->phone_number ?? '';
        $this->nid_number = $user->nid_number ?? '';
        $this->permanent_address = $user->permanent_address ?? '';
        $this->current_nid_image = $user->nid_image;
        $this->current_image = $user->image;
    }

    /**
     * Update the profile information for the currently authenticated user.
     */
    public function updateProfileInformation(): void
    {
        $user = Auth::user();

        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($user->id),
            ],
            'role' => ['required', 'in:' . implode(',', UserRole::values())],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'nid_number' => ['nullable', 'string', 'max:50'],
            'nid_image' => ['nullable', 'image', 'max:2048'],
            'image' => ['nullable', 'image', 'max:2048'],
            'permanent_address' => ['nullable', 'string', 'max:500'],
        ]);

        // Handle file uploads
        if ($this->nid_image) {
            // Delete old image if exists
            if ($user->nid_image) {
                Storage::disk('public')->delete($user->nid_image);
            }
            $validated['nid_image'] = $this->nid_image->store('nid-images', 'public');
        }

        if ($this->image) {
            // Delete old image if exists
            if ($user->image) {
                Storage::disk('public')->delete($user->image);
            }
            $validated['image'] = $this->image->store('profile-images', 'public');
        }

        $user->fill($validated);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();

        // Reset file inputs
        $this->reset(['nid_image', 'image']);
        $this->current_nid_image = $user->nid_image;
        $this->current_image = $user->image;

        $this->dispatch('profile-updated', name: $user->name);
    }

    /**
     * Send an email verification notification to the current user.
     */
    public function resendVerificationNotification(): void
    {
        $user = Auth::user();

        if ($user->hasVerifiedEmail()) {
            $this->redirectIntended(default: route('dashboard', absolute: false));

            return;
        }

        $user->sendEmailVerificationNotification();

        Session::flash('status', 'verification-link-sent');
    }
}
