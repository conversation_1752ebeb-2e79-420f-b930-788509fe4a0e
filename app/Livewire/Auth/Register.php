<?php

namespace App\Livewire\Auth;

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithFileUploads;

#[Layout('components.layouts.auth')]
class Register extends Component
{
    use WithFileUploads;

    public string $name = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public UserRole $role = UserRole::TENANT;
    public string $phone_number = '';
    public string $nid_number = '';
    public $nid_image;
    public $image;
    public string $permanent_address = '';

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'in:' . implode(',', UserRole::values())],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'nid_number' => ['nullable', 'string', 'max:50'],
            'nid_image' => ['nullable', 'image', 'max:2048'], // 2MB max
            'image' => ['nullable', 'image', 'max:2048'], // 2MB max
            'permanent_address' => ['nullable', 'string', 'max:500'],
        ]);

        $validated['password'] = Hash::make($validated['password']);

        // Handle file uploads
        if ($this->nid_image) {
            $validated['nid_image'] = $this->nid_image->store('nid-images', 'public');
        }

        if ($this->image) {
            $validated['image'] = $this->image->store('profile-images', 'public');
        }

        event(new Registered(($user = User::create($validated))));

        Auth::login($user);

        $this->redirect(route('dashboard', absolute: false), navigate: true);
    }
}
